#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to resolve all FeathersJS service main schemas to their final JSON schema format
 * This script imports all service schema files and uses the FeathersJS schema resolver
 * to resolve the main schema (not data, patch, or query schemas) to final JSON schemas.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// List of all services that have schema files
const servicesWithSchemas = [
  'ai-chats',
  'bank-accounts', 
  'bill-erasers',
  'bills',
  'budgets',
  'bundles',
  'calendars',
  'cams',
  'caps',
  'care-accounts',
  'cares',
  'cats',
  'challenges',
  'change-logs',
  'claim-payments',
  'claim-reqs',
  'claims',
  'cobras',
  'comps',
  'conditions',
  'contracts',
  'coverages',
  'cross-sections',
  'doc-requests',
  'doc-templates',
  'drops',
  'enrollments',
  'errs',
  'expenses',
  'fb-res',
  'fbs',
  'fingerprints',
  'flow-charts',
  'funds',
  'funds-requests',
  'gps',
  'groups',
  'grp-mbrs',
  'health-shares',
  'hosts',
  'households',
  'ims',
  'issues',
  'junk-drawers',
  'leads',
  'ledgers',
  'logins',
  'markets',
  'mbrs',
  'meds',
  'networks',
  'offers',
  'orgs',
  'passkeys',
  'pings',
  'plan-docs',
  'plans',
  'ppls',
  'practitioners',
  'price-estimates',
  'prices',
  'procedures',
  'providers',
  'rates',
  'refs',
  'reqs',
  'sales-taxes',
  'se-plans',
  'shops',
  'specs',
  'teams',
  'threads',
  'uploads',
  'visits',
  'wallets'
];

async function resolveAllSchemas() {
  const resolvedSchemas: Record<string, any> = {};
  const errors: Record<string, string> = {};

  console.log('Starting schema resolution for all services...\n');

  for (const serviceName of servicesWithSchemas) {
    try {
      console.log(`Processing ${serviceName}...`);

      // Import the schema file
      const schemaPath = `../src/services/${serviceName}/${serviceName}.schema.ts`;
      const schemaModule = await import(schemaPath);

      // Find the main schema (usually named like serviceName + 'Schema')
      const schemaKey = serviceName.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase()) + 'Schema';
      let mainSchema = schemaModule[schemaKey];

      if (!mainSchema) {
        // Try alternative naming patterns
        const altKeys = [
          serviceName.replace(/-/g, '') + 'Schema',
          serviceName.charAt(0).toUpperCase() + serviceName.slice(1).replace(/-([a-z])/g, (match, letter) => letter.toUpperCase()) + 'Schema'
        ];

        let found = false;
        for (const altKey of altKeys) {
          if (schemaModule[altKey]) {
            mainSchema = schemaModule[altKey];
            found = true;
            break;
          }
        }

        if (!found) {
          errors[serviceName] = `Main schema not found. Available exports: ${Object.keys(schemaModule).join(', ')}`;
          continue;
        }
      }

      // The schema is already in JSON Schema format, just store it directly
      resolvedSchemas[serviceName] = mainSchema;

      console.log(`✓ ${serviceName} schema resolved`);

    } catch (error: any) {
      console.log(`✗ ${serviceName} failed: ${error.message}`);
      errors[serviceName] = error.message;
    }
  }

  // Write the resolved schemas to a file
  const outputPath = path.join(__dirname, 'resolved-schemas.json');
  const output = {
    resolvedSchemas,
    errors,
    timestamp: new Date().toISOString(),
    totalServices: servicesWithSchemas.length,
    successfullyResolved: Object.keys(resolvedSchemas).length,
    failed: Object.keys(errors).length
  };

  fs.writeFileSync(outputPath, JSON.stringify(output, null, 2));
  
  console.log('\n=== Summary ===');
  console.log(`Total services: ${output.totalServices}`);
  console.log(`Successfully resolved: ${output.successfullyResolved}`);
  console.log(`Failed: ${output.failed}`);
  console.log(`Output written to: ${outputPath}`);
  
  if (Object.keys(errors).length > 0) {
    console.log('\n=== Errors ===');
    for (const [service, error] of Object.entries(errors)) {
      console.log(`${service}: ${error}`);
    }
  }
}

// Run the script
resolveAllSchemas().catch(console.error);
