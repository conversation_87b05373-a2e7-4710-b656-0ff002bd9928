{"resolvedSchemas": {}, "errors": {"ai-chats": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/ai-chats/ai-chats.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "bank-accounts": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/bank-accounts/bank-accounts.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "bill-erasers": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/bill-erasers/bill-erasers.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "bills": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/bills/bills.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "budgets": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/budgets/budgets.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "bundles": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/bundles/bundles.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "calendars": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/calendars/calendars.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "cams": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/cams/cams.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "caps": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/caps/caps.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "care-accounts": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/care-accounts/care-accounts.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "cares": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/cares/cares.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "cats": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/cats/cats.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "challenges": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/challenges/challenges.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "change-logs": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/change-logs/change-logs.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "claim-payments": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/claim-payments/claim-payments.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "claim-reqs": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/claim-reqs/claim-reqs.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "claims": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/claims/claims.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "cobras": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/cobras/cobras.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "comps": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/comps/comps.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "conditions": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/conditions/conditions.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "contracts": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/contracts/contracts.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "coverages": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/coverages/coverages.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "cross-sections": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/cross-sections/cross-sections.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "doc-requests": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/doc-requests/doc-requests.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "doc-templates": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/doc-templates/doc-templates.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "drops": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/drops/drops.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "enrollments": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/enrollments/enrollments.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "errs": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/errs/errs.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "expenses": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/expenses/expenses.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "fb-res": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/fb-res/fb-res.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "fbs": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/fbs/fbs.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "fingerprints": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/fingerprints/fingerprints.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "flow-charts": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/flow-charts/flow-charts.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "funds": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/funds/funds.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "funds-requests": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/funds-requests/funds-requests.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "gps": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/gps/gps.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "groups": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/groups/groups.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "grp-mbrs": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/grp-mbrs/grp-mbrs.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "health-shares": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/health-shares/health-shares.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "hosts": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/hosts/hosts.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "households": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/households/households.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "ims": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/ims/ims.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "issues": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/issues/issues.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "junk-drawers": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/junk-drawers/junk-drawers.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "leads": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/leads/leads.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "ledgers": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/ledgers/ledgers.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "logins": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/logins/logins.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "markets": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/markets/markets.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "mbrs": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/mbrs/mbrs.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "meds": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/meds/meds.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "networks": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/networks/networks.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "offers": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/offers/offers.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "orgs": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/orgs/orgs.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "passkeys": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/passkeys/passkeys.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "pings": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/pings/pings.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "plan-docs": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/plan-docs/plan-docs.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "plans": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/plans/plans.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "ppls": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/ppls/ppls.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "practitioners": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/practitioners/practitioners.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "price-estimates": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/price-estimates/price-estimates.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "prices": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/prices/prices.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "procedures": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/procedures/procedures.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "providers": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/providers/providers.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "rates": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/rates/rates.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "refs": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/refs/refs.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "reqs": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/reqs/reqs.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "sales-taxes": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/sales-taxes/sales-taxes.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "se-plans": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/se-plans/se-plans.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "shops": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/shops/shops.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "specs": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/specs/specs.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "teams": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/teams/teams.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "threads": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/threads/threads.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "uploads": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/uploads/uploads.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "visits": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/visits/visits.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js", "wallets": "Cannot find module '/Users/<USER>/Applications/commoncare/server/src/services/wallets/wallets.schema.js' imported from /Users/<USER>/Applications/commoncare/server/scripts/resolve-all-schemas.js"}, "timestamp": "2025-09-03T18:08:25.043Z", "totalServices": 75, "successfullyResolved": 0, "failed": 75}