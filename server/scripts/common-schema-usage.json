{"schemaUsage": {"bill-erasers": {"files": {"$comment": "***[object Object] used here***"}}, "bills": {"toAddress": {"$comment": "***[object Object] used here***"}, "fromAddress": {"$comment": "***[object Object] used here***"}, "logo": {"$comment": "***[object Object] used here***"}, "toPhone": {"$comment": "***[object Object] used here***"}, "fromPhone": {"$comment": "***[object Object] used here***"}, "properties": {"$comment": "***[object Object] used here***"}, "tax": {"$comment": "***[object Object] used here***"}}, "cats": {"avatar": {"$comment": "***[object Object] used here***"}, "images": {"$comment": "***[object Object] used here***"}}, "claims": {"properties": {"$comment": "***[object Object] used here***"}}, "comps": {"geo": {"$comment": "***[object Object] used here***"}, "video": {"$comment": "***[object Object] used here***"}}, "doc-requests": {"phone": {"$comment": "***[object Object] used here***"}}, "enrollments": {"address": {"$comment": "***[object Object] used here***"}}, "fbs": {"avatar": {"$comment": "***[object Object] used here***"}, "welcomeImage": {"$comment": "***[object Object] used here***"}, "finishImage": {"$comment": "***[object Object] used here***"}, "welcomeVideos": {"$comment": "***[object Object] used here***"}, "welcomeFiles": {"$comment": "***[object Object] used here***"}, "finishVideos": {"$comment": "***[object Object] used here***"}, "finishFiles": {"$comment": "***[object Object] used here***"}}, "gps": {"files": {"$comment": "***[object Object] used here***"}}, "health-shares": {"logo": {"$comment": "***[object Object] used here***"}, "guidelines": {"$comment": "***[object Object] used here***"}, "cc_video": {"$comment": "***[object Object] used here***"}, "video": {"$comment": "***[object Object] used here***"}}, "hosts": {"locations": {"$comment": "***[object Object] used here***"}, "avatar": {"$comment": "***[object Object] used here***"}, "phones": {"$comment": "***[object Object] used here***"}}, "households": {"address": {"$comment": "***[object Object] used here***"}}, "markets": {"geo": {"$comment": "***[object Object] used here***"}}, "networks": {"avatar": {"$comment": "***[object Object] used here***"}, "images": {"$comment": "***[object Object] used here***"}}, "orgs": {"identical": {"$comment": "***[object Object] used here***"}, "properties": {"$comment": "***[object Object] used here***"}, "patternProperties": {"$comment": "***[object Object] used here***"}, "amount": {"$comment": "***[object Object] used here***"}}, "ppls": {"avatar": {"$comment": "***[object Object] used here***"}, "phones": {"$comment": "***[object Object] used here***"}, "address": {"$comment": "***[object Object] used here***"}, "addresses": {"$comment": "***[object Object] used here***"}}, "practitioners": {"avatar": {"$comment": "***[object Object] used here***"}, "phone": {"$comment": "***[object Object] used here***"}, "phones": {"$comment": "***[object Object] used here***"}}, "price-estimates": {"files": {"$comment": "***[object Object] used here***"}}, "providers": {"address": {"$comment": "***[object Object] used here***"}, "addresses": {"$comment": "***[object Object] used here***"}, "properties": {"$comment": "***[object Object] used here***"}, "avatar": {"$comment": "***[object Object] used here***"}, "images": {"$comment": "***[object Object] used here***"}, "phone": {"$comment": "***[object Object] used here***"}, "phones": {"$comment": "***[object Object] used here***"}}, "refs": {"avatar": {"$comment": "***[object Object] used here***"}, "phone": {"$comment": "***[object Object] used here***"}}, "teams": {"avatar": {"$comment": "***[object Object] used here***"}, "phone": {"$comment": "***[object Object] used here***"}, "sms": {"$comment": "***[object Object] used here***"}}}, "errors": {}, "timestamp": "2025-09-03T18:25:44.152Z", "totalServices": 75, "servicesWithUsage": 21, "totalFieldsFound": 58}