{"schemaUsage": {"bill-erasers": {"files": {"$comment": "***imageSchema used here***"}}, "bills": {"toAddress": {"$comment": "***addressSchema used here***"}, "fromAddress": {"$comment": "***addressSchema used here***"}, "logo": {"$comment": "***imageSchema used here***"}, "toPhone": {"$comment": "***phoneSchema used here***"}, "fromPhone": {"$comment": "***phoneSchema used here***"}, "recurrence": {"$comment": "***rRuleSchema used here***"}, "tax": {"$comment": "***taxSchema used here***"}}, "cats": {"avatar": {"$comment": "***imageSchema used here***"}, "images": {"$comment": "***imageSchema used here***"}}, "claims": {"tax": {"$comment": "***taxSchema used here***"}}, "comps": {"geo": {"$comment": "***geoJsonSchema used here***"}, "video": {"$comment": "***imageSchema used here***"}}, "doc-requests": {"phone": {"$comment": "***phoneSchema used here***"}}, "enrollments": {"address": {"$comment": "***addressSchema used here***"}}, "fbs": {"avatar": {"$comment": "***imageSchema used here***"}, "welcomeImage": {"$comment": "***imageSchema used here***"}, "finishImage": {"$comment": "***imageSchema used here***"}, "welcomeVideos": {"$comment": "***imageSchema used here***"}, "welcomeFiles": {"$comment": "***imageSchema used here***"}, "finishVideos": {"$comment": "***imageSchema used here***"}, "finishFiles": {"$comment": "***imageSchema used here***"}}, "gps": {"files": {"$comment": "***imageSchema used here***"}}, "health-shares": {"logo": {"$comment": "***imageSchema used here***"}, "guidelines": {"$comment": "***imageSchema used here***"}, "cc_video": {"$comment": "***videoSchema used here***"}, "video": {"$comment": "***videoSchema used here***"}}, "hosts": {"locations": {"$comment": "***addressSchema used here***"}, "avatar": {"$comment": "***imageSchema used here***"}, "phones": {"$comment": "***phoneSchema used here***"}}, "households": {"address": {"$comment": "***addressSchema used here***"}}, "markets": {"geo": {"$comment": "***geoJsonSchema used here***"}}, "networks": {"avatar": {"$comment": "***imageSchema used here***"}, "images": {"$comment": "***imageSchema used here***"}}, "orgs": {"phone": {"$comment": "***phoneSchema used here***"}, "phones": {"$comment": "***phoneSchema used here***"}, "avatar": {"$comment": "***imageSchema used here***"}, "coverImage": {"$comment": "***imageSchema used here***"}, "images": {"$comment": "***imageSchema used here***"}, "address": {"$comment": "***addressSchema used here***"}, "support_address": {"$comment": "***addressSchema used here***"}, "addresses": {"$comment": "***addressSchema used here***"}}, "ppls": {"avatar": {"$comment": "***imageSchema used here***"}, "phones": {"$comment": "***phoneSchema used here***"}, "address": {"$comment": "***addressSchema used here***"}, "addresses": {"$comment": "***addressSchema used here***"}}, "practitioners": {"avatar": {"$comment": "***imageSchema used here***"}, "phone": {"$comment": "***phoneSchema used here***"}, "phones": {"$comment": "***phoneSchema used here***"}}, "price-estimates": {"files": {"$comment": "***imageSchema used here***"}}, "providers": {"address": {"$comment": "***addressSchema used here***"}, "addresses": {"$comment": "***addressSchema used here***"}, "geo": {"$comment": "***geoJsonFeature used here***"}, "avatar": {"$comment": "***imageSchema used here***"}, "images": {"$comment": "***imageSchema used here***"}, "phone": {"$comment": "***phoneSchema used here***"}, "phones": {"$comment": "***phoneSchema used here***"}, "locations": {"$comment": "***serviceAddressSchema used here***"}}, "refs": {"avatar": {"$comment": "***imageSchema used here***"}, "phone": {"$comment": "***phoneSchema used here***"}}, "teams": {"avatar": {"$comment": "***imageSchema used here***"}, "phone": {"$comment": "***phoneSchema used here***"}, "sms": {"$comment": "***phoneSchema used here***"}}}, "errors": {}, "timestamp": "2025-09-03T18:22:56.202Z", "totalServices": 75, "servicesWithUsage": 21, "totalFieldsFound": 63}