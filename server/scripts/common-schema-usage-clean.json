{"ai-chats": {"push": {"$comment": "***addToSet used here***"}}, "bill-erasers": {"push": {"$comment": "***addToSet used here***"}, "addToSet": {"$comment": "***addToSet used here***"}, "items": {"$comment": "***imageSchema used here***"}, "type": {"$comment": "***imageSchema used here***"}, "pull": {"$comment": "***pull used here***"}}, "bills": {"toAddress": {"$comment": "***addressSchema used here***"}, "fromAddress": {"$comment": "***addressSchema used here***"}, "logo": {"$comment": "***imageSchema used here***"}, "toPhone": {"$comment": "***phoneSchema used here***"}, "fromPhone": {"$comment": "***phoneSchema used here***"}, "recurrence": {"$comment": "***rRuleSchema used here***"}, "tax": {"$comment": "***taxSchema used here***"}}, "budgets": {"addToSet": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}}, "bundles": {"addToSet": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}}, "calendars": {"recurrence": {"$comment": "***rRuleSchema used here***"}}, "care-accounts": {"addToSet": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}}, "cares": {"addToSet": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}}, "cats": {"avatar": {"$comment": "***imageSchema used here***"}, "items": {"$comment": "***imageSchema used here***"}, "addToSet": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}}, "claim-payments": {"addToSet": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}}, "claims": {"addToSet": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}, "tax": {"$comment": "***taxSchema used here***"}}, "comps": {"geo": {"$comment": "***geoJsonSchema used here***"}, "video": {"$comment": "***imageSchema used here***"}}, "contracts": {"ack": {"$comment": "***mandate used here***"}}, "coverages": {"addToSet": {"$comment": "***addToSet used here***"}, "geo": {"$comment": "***geoJsonSchema used here***"}, "carrierLogo": {"$comment": "***imageSchema used here***"}, "items": {"$comment": "***imageSchema used here***"}, "pull": {"$comment": "***pull used here***"}, "video": {"$comment": "***videoSchema used here***"}}, "cross-sections": {"addToSet": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}}, "doc-requests": {"phone": {"$comment": "***phoneSchema used here***"}}, "drops": {"addToSet": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}}, "enrollments": {"address": {"$comment": "***addressSchema used here***"}}, "expenses": {"addToSet": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}}, "fbs": {"addToSet": {"$comment": "***addToSet used here***"}, "avatar": {"$comment": "***imageSchema used here***"}, "welcomeImage": {"$comment": "***imageSchema used here***"}, "items": {"$comment": "***imageSchema used here***"}, "finishImage": {"$comment": "***imageSchema used here***"}, "type": {"$comment": "***imageSchema used here***"}, "pull": {"$comment": "***pull used here***"}}, "gps": {"addToSet": {"$comment": "***addToSet used here***"}, "items": {"$comment": "***imageSchema used here***"}, "pull": {"$comment": "***pull used here***"}}, "groups": {"addToSet": {"$comment": "***addToSet used here***"}}, "health-shares": {"logo": {"$comment": "***imageSchema used here***"}, "guidelines": {"$comment": "***imageSchema used here***"}, "cc_video": {"$comment": "***videoSchema used here***"}, "video": {"$comment": "***videoSchema used here***"}}, "hosts": {"items": {"$comment": "***phoneSchema used here***"}, "type": {"$comment": "***phoneSchema used here***"}, "addToSet": {"$comment": "***addToSet used here***"}, "avatar": {"$comment": "***imageSchema used here***"}, "pull": {"$comment": "***pull used here***"}}, "households": {"address": {"$comment": "***addressSchema used here***"}, "addToSet": {"$comment": "***addToSet used here***"}}, "ims": {"addToSet": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}}, "issues": {"addToSet": {"$comment": "***addToSet used here***"}}, "logins": {"push": {"$comment": "***addToSet used here***"}}, "markets": {"geo": {"$comment": "***geoJsonSchema used here***"}}, "meds": {"addToSet": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}}, "networks": {"addToSet": {"$comment": "***addToSet used here***"}, "avatar": {"$comment": "***imageSchema used here***"}, "items": {"$comment": "***imageSchema used here***"}, "pull": {"$comment": "***pull used here***"}}, "orgs": {"phone": {"$comment": "***phoneSchema used here***"}, "items": {"$comment": "***addressSchema used here***"}, "avatar": {"$comment": "***imageSchema used here***"}, "coverImage": {"$comment": "***imageSchema used here***"}, "address": {"$comment": "***addressSchema used here***"}, "support_address": {"$comment": "***addressSchema used here***"}}, "plans": {"addToSet": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}}, "ppls": {"avatar": {"$comment": "***imageSchema used here***"}, "items": {"$comment": "***addressSchema used here***"}, "type": {"$comment": "***addressSchema used here***"}, "address": {"$comment": "***addressSchema used here***"}, "addToSet": {"$comment": "***addToSet used here***"}, "push": {"$comment": "***addToSet used here***"}, "firstName": {"$comment": "***<PERSON><PERSON><PERSON><PERSON> used here***"}, "lastName": {"$comment": "***<PERSON><PERSON><PERSON><PERSON> used here***"}, "name": {"$comment": "***<PERSON><PERSON><PERSON><PERSON> used here***"}, "pull": {"$comment": "***pull used here***"}}, "practitioners": {"avatar": {"$comment": "***imageSchema used here***"}, "phone": {"$comment": "***phoneSchema used here***"}, "items": {"$comment": "***phoneSchema used here***"}}, "price-estimates": {"addToSet": {"$comment": "***addToSet used here***"}, "items": {"$comment": "***imageSchema used here***"}}, "prices": {"addToSet": {"$comment": "***addToSet used here***"}, "push": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}}, "providers": {"address": {"$comment": "***addressSchema used here***"}, "items": {"$comment": "***serviceAddressSchema used here***"}, "geo": {"$comment": "***geoJsonFeature used here***"}, "avatar": {"$comment": "***imageSchema used here***"}, "addToSet": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}, "phone": {"$comment": "***phoneSchema used here***"}, "type": {"$comment": "***serviceAddressSchema used here***"}}, "refs": {"avatar": {"$comment": "***imageSchema used here***"}, "phone": {"$comment": "***phoneSchema used here***"}, "host": {"$comment": "***operator<PERSON><PERSON><PERSON> used here***"}}, "shops": {"consent_to_changes": {"$comment": "***mandate used here***"}, "decline_changes": {"$comment": "***mandate used here***"}, "pull": {"$comment": "***pull used here***"}}, "teams": {"addToSet": {"$comment": "***addToSet used here***"}, "avatar": {"$comment": "***imageSchema used here***"}, "phone": {"$comment": "***phoneSchema used here***"}, "sms": {"$comment": "***phoneSchema used here***"}, "pull": {"$comment": "***pull used here***"}}, "uploads": {"addToSet": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}}, "visits": {"addToSet": {"$comment": "***addToSet used here***"}, "pull": {"$comment": "***pull used here***"}}}