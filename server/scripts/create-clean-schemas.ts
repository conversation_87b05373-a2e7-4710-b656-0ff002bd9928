#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to create a clean version of resolved schemas with just service name -> schema mapping
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function createCleanSchemas() {
  try {
    // Read the resolved schemas file
    const resolvedSchemasPath = path.join(__dirname, 'resolved-schemas.json');
    const resolvedSchemasData = JSON.parse(fs.readFileSync(resolvedSchemasPath, 'utf8'));
    
    // Extract just the schemas without metadata
    const cleanSchemas = resolvedSchemasData.resolvedSchemas;
    
    // Write the clean version
    const cleanSchemasPath = path.join(__dirname, 'service-schemas.json');
    fs.writeFileSync(cleanSchemasPath, JSON.stringify(cleanSchemas, null, 2));
    
    console.log(`✓ Clean schemas written to: ${cleanSchemasPath}`);
    console.log(`✓ Total services: ${Object.keys(cleanSchemas).length}`);
    
    // Also create a TypeScript version for easier importing
    const tsContent = `// Auto-generated file containing all FeathersJS service main schemas
// Generated on: ${new Date().toISOString()}

export const serviceSchemas = ${JSON.stringify(cleanSchemas, null, 2)} as const;

export type ServiceSchemas = typeof serviceSchemas;

// Individual service schema types
${Object.keys(cleanSchemas).map(serviceName => {
  const schemaName = serviceName.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
  const typeName = schemaName.charAt(0).toUpperCase() + schemaName.slice(1) + 'Schema';
  return `export type ${typeName} = typeof serviceSchemas['${serviceName}'];`;
}).join('\n')}

// Helper to get a specific service schema
export function getServiceSchema<T extends keyof ServiceSchemas>(serviceName: T): ServiceSchemas[T] {
  return serviceSchemas[serviceName];
}
`;
    
    const tsPath = path.join(__dirname, 'service-schemas.ts');
    fs.writeFileSync(tsPath, tsContent);
    
    console.log(`✓ TypeScript version written to: ${tsPath}`);
    
  } catch (error: any) {
    console.error('Error creating clean schemas:', error.message);
    process.exit(1);
  }
}

// Run the script
createCleanSchemas().catch(console.error);
