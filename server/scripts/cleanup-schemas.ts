#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to clean up resolved schemas by removing commonFields properties
 * This removes the common fields that are spread into every schema via ...commonFields.properties
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// List of common fields to remove (from commonFields.properties)
const commonFieldsKeys = [
  'env',
  'host', 
  'ref',
  'changeLog',
  'editMap',
  'deleted',
  'session_fp',
  'deletedAt',
  'updatedAt',
  'createdAt',
  'createdBy',
  'updatedBy',
  'updatedByHistory'
];

function omit(obj: any, keys: string[]): any {
  if (!obj || typeof obj !== 'object') return obj;
  
  const result = { ...obj };
  for (const key of keys) {
    delete result[key];
  }
  return result;
}

function cleanSchema(schema: any): any {
  if (!schema || typeof schema !== 'object') return schema;
  
  // If this is a schema with properties, clean the properties
  if (schema.properties && typeof schema.properties === 'object') {
    return {
      ...schema,
      properties: omit(schema.properties, commonFieldsKeys)
    };
  }
  
  return schema;
}

async function cleanupSchemas() {
  try {
    console.log('Starting schema cleanup...\n');
    
    // Read the service schemas file
    const serviceSchemasPath = path.join(__dirname, 'service-schemas.json');
    const serviceSchemasData = JSON.parse(fs.readFileSync(serviceSchemasPath, 'utf8'));
    
    // Clean each schema
    const cleanedSchemas: Record<string, any> = {};
    let totalRemoved = 0;
    
    for (const [serviceName, schema] of Object.entries(serviceSchemasData)) {
      console.log(`Processing ${serviceName}...`);
      
      const originalSchema = schema as any;
      const cleanedSchema = cleanSchema(originalSchema);
      
      // Count how many common fields were removed
      const originalPropCount = originalSchema.properties ? Object.keys(originalSchema.properties).length : 0;
      const cleanedPropCount = cleanedSchema.properties ? Object.keys(cleanedSchema.properties).length : 0;
      const removedCount = originalPropCount - cleanedPropCount;
      
      if (removedCount > 0) {
        console.log(`  ✓ Removed ${removedCount} common fields`);
        totalRemoved += removedCount;
      } else {
        console.log(`  - No common fields found`);
      }
      
      cleanedSchemas[serviceName] = cleanedSchema;
    }
    
    // Write the cleaned schemas
    const cleanedSchemasPath = path.join(__dirname, 'service-schemas-clean.json');
    fs.writeFileSync(cleanedSchemasPath, JSON.stringify(cleanedSchemas, null, 2));
    
    console.log(`\n=== Summary ===`);
    console.log(`✓ Processed ${Object.keys(cleanedSchemas).length} services`);
    console.log(`✓ Removed ${totalRemoved} total common field instances`);
    console.log(`✓ Clean schemas written to: ${cleanedSchemasPath}`);
    
    // Also create a TypeScript version
    const tsContent = `// Auto-generated file containing cleaned FeathersJS service schemas
// Common fields removed: ${commonFieldsKeys.join(', ')}
// Generated on: ${new Date().toISOString()}

export const cleanServiceSchemas = ${JSON.stringify(cleanedSchemas, null, 2)} as const;

export type CleanServiceSchemas = typeof cleanServiceSchemas;

// Individual clean service schema types
${Object.keys(cleanedSchemas).map(serviceName => {
  const schemaName = serviceName.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
  const typeName = 'Clean' + schemaName.charAt(0).toUpperCase() + schemaName.slice(1) + 'Schema';
  return `export type ${typeName} = typeof cleanServiceSchemas['${serviceName}'];`;
}).join('\n')}

// Helper to get a specific clean service schema
export function getCleanServiceSchema<T extends keyof CleanServiceSchemas>(serviceName: T): CleanServiceSchemas[T] {
  return cleanServiceSchemas[serviceName];
}

// List of removed common fields
export const removedCommonFields = ${JSON.stringify(commonFieldsKeys, null, 2)} as const;
`;
    
    const cleanTsPath = path.join(__dirname, 'service-schemas-clean.ts');
    fs.writeFileSync(cleanTsPath, tsContent);
    
    console.log(`✓ TypeScript version written to: ${cleanTsPath}`);
    
  } catch (error: any) {
    console.error('Error cleaning schemas:', error.message);
    process.exit(1);
  }
}

// Run the script
cleanupSchemas().catch(console.error);
