#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to find all patternProperties usage in service schemas
 * Creates a JSON file with service name as key and fields using patternProperties as values
 * Uses dot notation for nested paths
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

interface PatternPropertiesUsage {
  [serviceName: string]: {
    [fieldPath: string]: {
      type: "object";
      patternProperties: any;
    };
  };
}

function findPatternProperties(obj: any, currentPath: string = ''): Record<string, any> {
  const results: Record<string, any> = {};
  
  if (!obj || typeof obj !== 'object') return results;
  
  // Check if this object has patternProperties
  if (obj.type === 'object' && obj.patternProperties) {
    results[currentPath || 'root'] = {
      type: 'object',
      patternProperties: obj.patternProperties
    };
  }
  
  // Recursively search through properties
  if (obj.properties && typeof obj.properties === 'object') {
    for (const [key, value] of Object.entries(obj.properties)) {
      const newPath = currentPath ? `${currentPath}.${key}` : key;
      const nestedResults = findPatternProperties(value, newPath);
      Object.assign(results, nestedResults);
    }
  }
  
  // Search through array items
  if (obj.items && typeof obj.items === 'object') {
    const itemsPath = currentPath ? `${currentPath}.items` : 'items';
    const nestedResults = findPatternProperties(obj.items, itemsPath);
    Object.assign(results, nestedResults);
  }
  
  // Search through patternProperties themselves (nested patterns)
  if (obj.patternProperties && typeof obj.patternProperties === 'object') {
    for (const [pattern, value] of Object.entries(obj.patternProperties)) {
      const patternPath = currentPath ? `${currentPath}[${pattern}]` : `[${pattern}]`;
      const nestedResults = findPatternProperties(value, patternPath);
      Object.assign(results, nestedResults);
    }
  }
  
  // Search through other object properties that might contain schemas
  for (const [key, value] of Object.entries(obj)) {
    if (key !== 'properties' && key !== 'items' && key !== 'patternProperties' && 
        typeof value === 'object' && value !== null) {
      const nestedResults = findPatternProperties(value, currentPath);
      Object.assign(results, nestedResults);
    }
  }
  
  return results;
}

function processServiceSchemas() {
  try {
    console.log('Finding patternProperties usage in service schemas...\n');
    
    // Read the service schemas file
    const inputPath = path.join(__dirname, 'service-schemas-clean.json');
    const serviceSchemas = JSON.parse(fs.readFileSync(inputPath, 'utf8'));
    
    const patternPropertiesUsage: PatternPropertiesUsage = {};
    let totalPatternProperties = 0;
    
    // Process each service schema
    for (const [serviceName, schema] of Object.entries(serviceSchemas)) {
      console.log(`Processing ${serviceName}...`);
      
      const patternProps = findPatternProperties(schema);
      
      if (Object.keys(patternProps).length > 0) {
        // Clean up the paths - remove 'root' and fix dot notation
        const cleanedPatternProps: Record<string, any> = {};
        for (const [path, value] of Object.entries(patternProps)) {
          let cleanPath = path;
          if (cleanPath === 'root') {
            cleanPath = serviceName; // Use service name for root level
          } else if (cleanPath.startsWith('properties.')) {
            cleanPath = cleanPath.replace('properties.', '');
          }
          cleanedPatternProps[cleanPath] = value;
        }
        
        patternPropertiesUsage[serviceName] = cleanedPatternProps;
        const count = Object.keys(cleanedPatternProps).length;
        console.log(`  ✓ Found ${count} patternProperties usage(s)`);
        totalPatternProperties += count;
      } else {
        console.log(`  - No patternProperties found`);
      }
    }
    
    // Write the results
    const outputPath = path.join(__dirname, 'pattern-properties-usage.json');
    const output = {
      patternPropertiesUsage,
      summary: {
        totalServices: Object.keys(serviceSchemas).length,
        servicesWithPatternProperties: Object.keys(patternPropertiesUsage).length,
        totalPatternPropertiesFound: totalPatternProperties,
        timestamp: new Date().toISOString()
      }
    };
    
    fs.writeFileSync(outputPath, JSON.stringify(output, null, 2));
    
    // Also create a clean version with just the usage data
    const cleanOutputPath = path.join(__dirname, 'pattern-properties-usage-clean.json');
    fs.writeFileSync(cleanOutputPath, JSON.stringify(patternPropertiesUsage, null, 2));
    
    console.log(`\n=== Summary ===`);
    console.log(`✓ Processed ${output.summary.totalServices} services`);
    console.log(`✓ Found patternProperties in ${output.summary.servicesWithPatternProperties} services`);
    console.log(`✓ Total patternProperties found: ${output.summary.totalPatternPropertiesFound}`);
    console.log(`✓ Output written to: ${outputPath}`);
    console.log(`✓ Clean version written to: ${cleanOutputPath}`);
    
  } catch (error: any) {
    console.error('Error processing schemas:', error.message);
    process.exit(1);
  }
}

// Run the script
processServiceSchemas();
