#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to find all fields in service schemas that use common schema utilities
 * Creates a JSON file with service name as key and fields using common schemas as values
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// List of all services that have schema files
const servicesWithSchemas = [
  'ai-chats', 'bank-accounts', 'bill-erasers', 'bills', 'budgets', 'bundles',
  'calendars', 'cams', 'caps', 'care-accounts', 'cares', 'cats', 'challenges',
  'change-logs', 'claim-payments', 'claim-reqs', 'claims', 'cobras', 'comps',
  'conditions', 'contracts', 'coverages', 'cross-sections', 'doc-requests',
  'doc-templates', 'drops', 'enrollments', 'errs', 'expenses', 'fb-res', 'fbs',
  'fingerprints', 'flow-charts', 'funds', 'funds-requests', 'gps', 'groups',
  'grp-mbrs', 'health-shares', 'hosts', 'households', 'ims', 'issues',
  'junk-drawers', 'leads', 'ledgers', 'logins', 'markets', 'mbrs', 'meds',
  'networks', 'offers', 'orgs', 'passkeys', 'pings', 'plan-docs', 'plans',
  'ppls', 'practitioners', 'price-estimates', 'prices', 'procedures', 'providers',
  'rates', 'refs', 'reqs', 'sales-taxes', 'se-plans', 'shops', 'specs', 'teams',
  'threads', 'uploads', 'visits', 'wallets'
];

// Common schema utilities to look for (from common/schemas.ts)
const commonSchemaUtilities = [
  'imageSchema',
  'videoSchema', 
  'phoneSchema',
  'addressSchema',
  'serviceAddressSchema',
  'geoJsonSchema',
  'geoJsonFeature',
  'rRuleSchema',
  'taxSchema',
  'idListQuerySchema',
  'effectiveAny',
  'addToSet',
  'pull',
  'exists',
  'existsQuery',
  'operatorQuery',
  'trimHandler',
  'mandate'
];

interface SchemaUsage {
  [serviceName: string]: {
    [fieldName: string]: {
      $comment: string;
    };
  };
}

function analyzeSchemaFile(filePath: string): { imports: string[], content: string } {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Extract imports from common schemas
    const importMatches = content.match(/import\s*\{([^}]+)\}\s*from\s*['"].*\/common\/schemas\.js['"];?/);
    const imports = importMatches 
      ? importMatches[1].split(',').map(imp => imp.trim()).filter(imp => imp.length > 0)
      : [];
    
    return { imports, content };
  } catch (error) {
    return { imports: [], content: '' };
  }
}

function findFieldsUsingSchemas(content: string, imports: string[]): Record<string, string> {
  const fieldsUsing: Record<string, string> = {};
  
  // Look for direct usage of imported schemas in property definitions
  for (const schemaName of imports) {
    if (!commonSchemaUtilities.includes(schemaName)) continue;
    
    // Pattern to match field: schemaName or field: { ...schemaName.properties }
    const patterns = [
      new RegExp(`(\\w+):\\s*${schemaName}(?![\\w])`, 'g'),
      new RegExp(`(\\w+):\\s*\\{[^}]*\\.\\.\\.${schemaName}\\.properties`, 'g'),
      new RegExp(`items:\\s*${schemaName}(?![\\w])`, 'g') // for arrays
    ];
    
    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const fieldName = match[1] || 'items'; // 'items' for array items
        if (fieldName && fieldName !== 'properties') {
          fieldsUsing[fieldName] = schemaName;
        }
      }
    }
  }
  
  return fieldsUsing;
}

async function findCommonSchemaUsage() {
  const schemaUsage: SchemaUsage = {};
  const errors: Record<string, string> = {};
  
  console.log('Analyzing service schema files for common schema usage...\n');
  
  for (const serviceName of servicesWithSchemas) {
    try {
      console.log(`Processing ${serviceName}...`);
      
      const schemaFilePath = path.join(__dirname, `../src/services/${serviceName}/${serviceName}.schema.ts`);
      
      if (!fs.existsSync(schemaFilePath)) {
        console.log(`  - Schema file not found`);
        continue;
      }
      
      const { imports, content } = analyzeSchemaFile(schemaFilePath);
      
      if (imports.length === 0) {
        console.log(`  - No common schema imports found`);
        continue;
      }
      
      const fieldsUsing = findFieldsUsingSchemas(content, imports);
      
      if (Object.keys(fieldsUsing).length === 0) {
        console.log(`  - No fields using common schemas found`);
        continue;
      }
      
      // Create the schema usage object for this service
      schemaUsage[serviceName] = {};
      for (const [fieldName, schemaName] of Object.entries(fieldsUsing)) {
        schemaUsage[serviceName][fieldName] = {
          $comment: `***${schemaName} used here***`
        };
      }
      
      console.log(`  ✓ Found ${Object.keys(fieldsUsing).length} fields using common schemas`);
      
    } catch (error: any) {
      console.log(`  ✗ Error: ${error.message}`);
      errors[serviceName] = error.message;
    }
  }
  
  // Write the results
  const outputPath = path.join(__dirname, 'common-schema-usage.json');
  const output = {
    schemaUsage,
    errors,
    timestamp: new Date().toISOString(),
    totalServices: servicesWithSchemas.length,
    servicesWithUsage: Object.keys(schemaUsage).length,
    totalFieldsFound: Object.values(schemaUsage).reduce((sum, service) => sum + Object.keys(service).length, 0)
  };
  
  fs.writeFileSync(outputPath, JSON.stringify(output, null, 2));
  
  console.log('\n=== Summary ===');
  console.log(`✓ Processed ${output.totalServices} services`);
  console.log(`✓ Found usage in ${output.servicesWithUsage} services`);
  console.log(`✓ Total fields using common schemas: ${output.totalFieldsFound}`);
  console.log(`✓ Output written to: ${outputPath}`);
  
  // Also create a clean version with just the schema usage
  const cleanOutputPath = path.join(__dirname, 'common-schema-usage-clean.json');
  fs.writeFileSync(cleanOutputPath, JSON.stringify(schemaUsage, null, 2));
  console.log(`✓ Clean version written to: ${cleanOutputPath}`);
  
  if (Object.keys(errors).length > 0) {
    console.log('\n=== Errors ===');
    for (const [service, error] of Object.entries(errors)) {
      console.log(`${service}: ${error}`);
    }
  }
}

// Run the script
findCommonSchemaUsage().catch(console.error);
