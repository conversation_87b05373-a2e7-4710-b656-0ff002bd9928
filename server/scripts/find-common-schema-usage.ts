#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to find all fields in service schemas that use common schema utilities
 * Creates a JSON file with service name as key and fields using common schemas as values
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// List of all services that have schema files
const servicesWithSchemas = [
  'ai-chats', 'bank-accounts', 'bill-erasers', 'bills', 'budgets', 'bundles',
  'calendars', 'cams', 'caps', 'care-accounts', 'cares', 'cats', 'challenges',
  'change-logs', 'claim-payments', 'claim-reqs', 'claims', 'cobras', 'comps',
  'conditions', 'contracts', 'coverages', 'cross-sections', 'doc-requests',
  'doc-templates', 'drops', 'enrollments', 'errs', 'expenses', 'fb-res', 'fbs',
  'fingerprints', 'flow-charts', 'funds', 'funds-requests', 'gps', 'groups',
  'grp-mbrs', 'health-shares', 'hosts', 'households', 'ims', 'issues',
  'junk-drawers', 'leads', 'ledgers', 'logins', 'markets', 'mbrs', 'meds',
  'networks', 'offers', 'orgs', 'passkeys', 'pings', 'plan-docs', 'plans',
  'ppls', 'practitioners', 'price-estimates', 'prices', 'procedures', 'providers',
  'rates', 'refs', 'reqs', 'sales-taxes', 'se-plans', 'shops', 'specs', 'teams',
  'threads', 'uploads', 'visits', 'wallets'
];

// Common schema utilities to look for (from common/schemas.ts)
// Excluding MongoDB operators and hook utilities that belong in patch schemas
const commonSchemaUtilities = [
  'imageSchema',
  'videoSchema',
  'phoneSchema',
  'addressSchema',
  'serviceAddressSchema',
  'geoJsonSchema',
  'geoJsonFeature',
  'rRuleSchema',
  'taxSchema',
  'idListQuerySchema',
  'effectiveAny'
];

interface SchemaUsage {
  [serviceName: string]: {
    [fieldName: string]: {
      $comment: string;
    };
  };
}

function analyzeSchemaFile(filePath: string): { imports: string[], content: string } {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Extract imports from common schemas
    const importMatches = content.match(/import\s*\{([^}]+)\}\s*from\s*['"].*\/common\/schemas\.js['"];?/);
    const imports = importMatches 
      ? importMatches[1].split(',').map(imp => imp.trim()).filter(imp => imp.length > 0)
      : [];
    
    return { imports, content };
  } catch (error) {
    return { imports: [], content: '' };
  }
}

function findMainSchemaAndFields(content: string, serviceName: string, imports: string[]): Record<string, string> {
  const fieldsUsing: Record<string, string> = {};

  // Find the main schema export (not patch, data, or query schemas)
  const schemaKey = serviceName.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase()) + 'Schema';

  // More robust pattern to extract the main schema with proper brace matching
  const mainSchemaPattern = new RegExp(`export\\s+const\\s+${schemaKey}\\s*=\\s*\\{`, 's');
  const mainSchemaStart = content.search(mainSchemaPattern);

  if (mainSchemaStart === -1) {
    // Try alternative naming patterns
    const altKeys = [
      serviceName.replace(/-/g, '') + 'Schema',
      serviceName.charAt(0).toUpperCase() + serviceName.slice(1).replace(/-([a-z])/g, (match, letter) => letter.toUpperCase()) + 'Schema'
    ];

    for (const altKey of altKeys) {
      const altPattern = new RegExp(`export\\s+const\\s+${altKey}\\s*=\\s*\\{`, 's');
      const altStart = content.search(altPattern);
      if (altStart !== -1) {
        const schemaContent = extractSchemaContent(content, altStart);
        return findFieldsInSchemaContent(schemaContent, imports);
      }
    }
    return fieldsUsing;
  }

  const schemaContent = extractSchemaContent(content, mainSchemaStart);
  return findFieldsInSchemaContent(schemaContent, imports);
}

function extractSchemaContent(content: string, startPos: number): string {
  // Find the opening brace
  const openBracePos = content.indexOf('{', startPos);
  if (openBracePos === -1) return '';

  // Count braces to find the matching closing brace
  let braceCount = 0;
  let pos = openBracePos;

  while (pos < content.length) {
    if (content[pos] === '{') braceCount++;
    if (content[pos] === '}') braceCount--;
    if (braceCount === 0) break;
    pos++;
  }

  return content.substring(openBracePos + 1, pos);
}

function findFieldsInSchemaContent(schemaContent: string, imports: string[]): Record<string, any> {
  const fieldsUsing: Record<string, any> = {};

  // Filter out MongoDB operators and other non-schema utilities
  const schemaUtilitiesOnly = imports.filter(imp =>
    commonSchemaUtilities.includes(imp) &&
    !['addToSet', 'pull', 'exists', 'existsQuery', 'operatorQuery', 'trimHandler', 'mandate'].includes(imp)
  );

  // Look for direct usage of imported schemas in property definitions within the main schema
  for (const schemaName of schemaUtilitiesOnly) {
    // More comprehensive patterns to match various usage styles
    const patterns = [
      // Direct assignment: fieldName: schemaName
      new RegExp(`(\\w+):\\s*${schemaName}(?![\\w.])`, 'g'),
      // Spread properties: fieldName: { ...schemaName.properties }
      new RegExp(`(\\w+):\\s*\\{[^}]*\\.\\.\\.${schemaName}\\.properties`, 'g'),
      // Array items: items: schemaName
      new RegExp(`items:\\s*${schemaName}(?![\\w.])`, 'g'),
      // Array with direct schema: { type: 'array', items: schemaName }
      new RegExp(`(\\w+):\\s*\\{[^}]*items:\\s*${schemaName}(?![\\w.])`, 'g')
    ];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(schemaContent)) !== null) {
        let fieldName = match[1];

        // Special handling for array items pattern
        if (!fieldName && pattern.source.includes('items:')) {
          // Look backwards to find the field name for this array
          const beforeMatch = schemaContent.substring(0, match.index);
          const fieldMatch = beforeMatch.match(/(\\w+):\\s*\\{[^}]*$/);
          if (fieldMatch) {
            fieldName = fieldMatch[1];
          }
        }

        if (fieldName && fieldName !== 'properties' && fieldName !== 'type' && fieldName !== 'items') {
          // Check if this field is nested by looking for the context
          const fieldPath = findNestedFieldPath(schemaContent, match.index, fieldName);
          const commentValue = `***${schemaName} used here***`;
          setNestedPath(fieldsUsing, fieldPath, { $comment: commentValue });
        }
      }
    }
  }

  return fieldsUsing;
}

function findNestedFieldPath(content: string, matchIndex: number, fieldName: string): string {
  // Simple approach: look backwards from the match to find parent object contexts
  const beforeMatch = content.substring(0, matchIndex);
  const lines = beforeMatch.split('\n');
  const parentFields: string[] = [];

  // Work backwards through lines to find parent object definitions
  let braceDepth = 0;
  for (let i = lines.length - 1; i >= 0; i--) {
    const line = lines[i].trim();

    // Count braces to track nesting
    const openBraces = (line.match(/\{/g) || []).length;
    const closeBraces = (line.match(/\}/g) || []).length;
    braceDepth += closeBraces - openBraces;

    // Look for parent field definitions
    const parentMatch = line.match(/^(\w+):\s*\{/);
    if (parentMatch && braceDepth > 0) {
      parentFields.unshift(parentMatch[1]);
    }

    // Stop when we reach the root level
    if (braceDepth <= 0 && parentFields.length > 0) {
      break;
    }
  }

  // Build the full path
  if (parentFields.length > 0) {
    return [...parentFields, fieldName].join('.');
  }

  return fieldName;
}

function setNestedPath(obj: Record<string, any>, path: string, value: any): void {
  const parts = path.split('.');
  let current = obj;

  for (let i = 0; i < parts.length - 1; i++) {
    const part = parts[i];
    if (!(part in current)) {
      current[part] = {};
    }
    current = current[part];
  }

  current[parts[parts.length - 1]] = value;
}

async function findCommonSchemaUsage() {
  const schemaUsage: SchemaUsage = {};
  const errors: Record<string, string> = {};
  
  console.log('Analyzing service schema files for common schema usage...\n');
  
  for (const serviceName of servicesWithSchemas) {
    try {
      console.log(`Processing ${serviceName}...`);
      
      const schemaFilePath = path.join(__dirname, `../src/services/${serviceName}/${serviceName}.schema.ts`);
      
      if (!fs.existsSync(schemaFilePath)) {
        console.log(`  - Schema file not found`);
        continue;
      }
      
      const { imports, content } = analyzeSchemaFile(schemaFilePath);
      
      if (imports.length === 0) {
        console.log(`  - No common schema imports found`);
        continue;
      }
      
      const fieldsUsing = findMainSchemaAndFields(content, serviceName, imports);
      
      if (Object.keys(fieldsUsing).length === 0) {
        console.log(`  - No fields using common schemas found`);
        continue;
      }
      
      // Create the schema usage object for this service
      schemaUsage[serviceName] = {};
      for (const [fieldName, schemaName] of Object.entries(fieldsUsing)) {
        schemaUsage[serviceName][fieldName] = {
          $comment: `***${schemaName} used here***`
        };
      }
      
      console.log(`  ✓ Found ${Object.keys(fieldsUsing).length} fields using common schemas`);
      
    } catch (error: any) {
      console.log(`  ✗ Error: ${error.message}`);
      errors[serviceName] = error.message;
    }
  }
  
  // Write the results
  const outputPath = path.join(__dirname, 'common-schema-usage.json');
  const output = {
    schemaUsage,
    errors,
    timestamp: new Date().toISOString(),
    totalServices: servicesWithSchemas.length,
    servicesWithUsage: Object.keys(schemaUsage).length,
    totalFieldsFound: Object.values(schemaUsage).reduce((sum, service) => sum + Object.keys(service).length, 0)
  };
  
  fs.writeFileSync(outputPath, JSON.stringify(output, null, 2));
  
  console.log('\n=== Summary ===');
  console.log(`✓ Processed ${output.totalServices} services`);
  console.log(`✓ Found usage in ${output.servicesWithUsage} services`);
  console.log(`✓ Total fields using common schemas: ${output.totalFieldsFound}`);
  console.log(`✓ Output written to: ${outputPath}`);
  
  // Also create a clean version with just the schema usage
  const cleanOutputPath = path.join(__dirname, 'common-schema-usage-clean.json');
  fs.writeFileSync(cleanOutputPath, JSON.stringify(schemaUsage, null, 2));
  console.log(`✓ Clean version written to: ${cleanOutputPath}`);
  
  if (Object.keys(errors).length > 0) {
    console.log('\n=== Errors ===');
    for (const [service, error] of Object.entries(errors)) {
      console.log(`${service}: ${error}`);
    }
  }
}

// Run the script
findCommonSchemaUsage().catch(console.error);
