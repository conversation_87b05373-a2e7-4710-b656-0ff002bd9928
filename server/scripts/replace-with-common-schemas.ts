#!/usr/bin/env node

/**
 * <PERSON>ript to replace field patterns in service schemas with common schema indicators
 * Processes service-schemas-clean.json and replaces matching patterns with $comment indicators
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Schema patterns to look for, in order of priority (first match wins)
const schemaPatterns = {
  geoJsonSchema: ['allowFeatures', 'features'],
  imageSchema: ['uploadId', 'fileId'],
  videoSchema: ['author_name', 'thumbnail_url'],
  updatedBySchema: ['longtail', 'updatedBy'],
  updatesSchema: ['longtail', 'origin'],
  serviceAddressSchema: ['address1', 'geo'],
  addressSchema: ['address1', 'googleAddress'],
  taxSchema: ['automateTaxes', 'taxExempt']
};

interface SchemaObject {
  [key: string]: any;
}

function hasAllFields(obj: any, fields: string[]): boolean {
  if (!obj || typeof obj !== 'object') return false;
  
  // Handle array items
  if (obj.type === 'array' && obj.items) {
    return hasAllFields(obj.items, fields);
  }
  
  // Check if object has properties
  if (obj.properties) {
    return fields.every(field => field in obj.properties);
  }
  
  // Check direct properties
  return fields.every(field => field in obj);
}

function replaceWithSchemaComment(obj: any, schemaName: string): any {
  return {
    $comment: `***${schemaName} used here***`
  };
}

function processSchemaObject(obj: any): any {
  if (!obj || typeof obj !== 'object') return obj;
  
  // Handle arrays
  if (Array.isArray(obj)) {
    return obj.map(item => processSchemaObject(item));
  }
  
  // Check for schema pattern matches (in order)
  for (const [schemaName, fields] of Object.entries(schemaPatterns)) {
    if (hasAllFields(obj, fields)) {
      return replaceWithSchemaComment(obj, schemaName);
    }
  }
  
  // Recursively process nested objects
  const result: any = {};
  for (const [key, value] of Object.entries(obj)) {
    if (key === 'properties' && typeof value === 'object') {
      // Process each property in the properties object
      result[key] = {};
      for (const [propKey, propValue] of Object.entries(value as object)) {
        result[key][propKey] = processSchemaObject(propValue);
      }
    } else if (key === 'items' && typeof value === 'object') {
      // Process array items
      result[key] = processSchemaObject(value);
    } else if (typeof value === 'object' && value !== null) {
      result[key] = processSchemaObject(value);
    } else {
      result[key] = value;
    }
  }
  
  return result;
}

function processServiceSchemas() {
  try {
    console.log('Processing service schemas to replace with common schema indicators...\n');
    
    // Read the clean service schemas file
    const inputPath = path.join(__dirname, 'service-schemas-clean.json');
    const serviceSchemas = JSON.parse(fs.readFileSync(inputPath, 'utf8'));
    
    let totalReplacements = 0;
    const processedSchemas: Record<string, any> = {};
    
    // Process each service schema
    for (const [serviceName, schema] of Object.entries(serviceSchemas)) {
      console.log(`Processing ${serviceName}...`);
      
      const originalJson = JSON.stringify(schema);
      const processedSchema = processSchemaObject(schema);
      const processedJson = JSON.stringify(processedSchema);
      
      const replacements = (originalJson.match(/\$comment/g) || []).length - 
                          (processedJson.match(/\$comment/g) || []).length;
      
      if (replacements !== 0) {
        console.log(`  ✓ Made ${Math.abs(replacements)} replacements`);
        totalReplacements += Math.abs(replacements);
      } else {
        console.log(`  - No replacements made`);
      }
      
      processedSchemas[serviceName] = processedSchema;
    }
    
    // Write the processed schemas
    const outputPath = path.join(__dirname, 'service-schemas-with-common-indicators.json');
    fs.writeFileSync(outputPath, JSON.stringify(processedSchemas, null, 2));
    
    console.log(`\n=== Summary ===`);
    console.log(`✓ Processed ${Object.keys(processedSchemas).length} services`);
    console.log(`✓ Total replacements made: ${totalReplacements}`);
    console.log(`✓ Output written to: ${outputPath}`);
    
  } catch (error: any) {
    console.error('Error processing schemas:', error.message);
    process.exit(1);
  }
}

// Run the script
processServiceSchemas();
