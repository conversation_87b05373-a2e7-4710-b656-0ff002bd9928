<template>
  <q-page class="bg-a12">
    <div class="row justify-center __bg">
      <div class="_cent __pd pd15">
        <div class="row">
          <div class="col-12 __col">
            <div class="row justify-center">
              <div class="_sent">
                <div class="text-xl text-center tw-six">See <span class="text-primary">exactly</span> how much federal
                  tax
                  credit your health plan is missing
                </div>
                <q-separator class="q-my-sm"></q-separator>
                <div class="text-sm text-center">Precise results in seconds - no personal information required.
                </div>
              </div>
            </div>

          </div>

          <div class="col-12 __col text-black">
            <q-tab-panels keep-alive class="_panel" v-model="tab" animated @update:model-value="tab = 'result'">
              <q-tab-panel class="_panel" name="census">
                <div class="_fw">
                  <q-chip dark clickable square dense color="transparent" @click="startOver">
                    <q-icon name="mdi-refresh" color="accent" class="q-mr-sm"></q-icon>
                    <span>Start Over</span>
                  </q-chip>
                </div>
                <div class="__census">
                  <census-table
                      service-path="doc-requests"
                      :store="drStore"
                      save-btn
                      v-model:id="drId"
                      :update="updateCensus"
                  >
                    <template v-slot:top>
                      <div class="q-py-lg tw-six text-xxs text-center">Copy/Paste your census data into the table below
                      </div>
                    </template>
                  </census-table>
                </div>
              </q-tab-panel>
              <q-tab-panel class="_panel" name="result">

                <analysis-display
                    @back="tab = 'census'"
                    @one="setOne"
                    :model-value="docReq"
                    v-model:changes="changes"
                ></analysis-display>

              </q-tab-panel>
              <q-tab-panel class="_panel" name="one">
                <q-btn @click="tab = 'result'" flat size="sm" no-caps class="tw-six">
                  <q-icon name="mdi-chevron-left" color="accent" class="q-mr-xs"></q-icon>
                  <span class="text-white">Back to group</span>
                </q-btn>
                <one-employee v-if="tab === 'one'" :req="docReq" :uid="uid"></one-employee>
              </q-tab-panel>
            </q-tab-panels>
          </div>
        </div>

      </div>
    </div>
  </q-page>
</template>

<script setup>
  import CensusTable from 'pages/landing/sm-er/results/CensusTable.vue';
  import AnalysisDisplay from 'pages/landing/sm-er/results/AnalysisDisplay.vue';
  import OneEmployee from 'pages/landing/sm-er/results/OneEmployee.vue';

  import {computed, onMounted, ref, watch} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useDocRequests} from 'stores/doc-requests';
  import {LocalStorage} from 'symbol-auth-client';
  import {useRoute, useRouter} from 'vue-router';

  const route = useRoute();
  const router = useRouter();

  const drStore = useDocRequests();

  const tab = ref('census');
  const checked = ref(false);
  const loading = ref(false);

  const drId = ref()
  const { item: docReq } = idGet({
    store: drStore,
    def: '',
    value: computed(() => drId.value || route.params.quoteId || LocalStorage.getItem('docReq'))
  })

  const updateCensus = async (employees, data, auto) => {
    let method = 'create';
    const args = [{ employees, eeCount: data?.length || employees.length }]
    if (docReq.value?._id || data._id) {
      method = 'patch';
      args.unshift(docReq.value._id || data._id);
    }
    const dr = await drStore[method](...args)
        .catch(err => {
          console.error(`Error adding dr: ${err.message}`);
          loading.value = false;
        })
    if (dr._id) {
      router.push({ ...route, params: { quoteId: dr._id } })
      drId.value = dr._id
      LocalStorage.setItem('docReq', dr._id);
      changes.value = true;
      if(!auto) tab.value = 'result'
    }
  }

  watch(docReq, (val) => {
    if (val) {
      // console.log('set dr id', val);
      drId.value = val._id;
      if (!checked.value) {
        checked.value = true;
        const ees = val.employees || [];
        let t = 'result'
        // console.log('checking now')
        for (let i = 0; i < ees.length; i++) {
          // console.log('does it?', ees[i].ptcUpdatedAt)
          if (!ees[i].ptcUpdatedAt) {
            t = 'census'
            break;
          }
        }
        tab.value = t;
      }
    }
  }, { immediate: true });
  const changes = ref(false);

  const uid = ref();
  const setOne = (val) => {
    tab.value = 'one'
    uid.value = val;
  }

  onMounted(() => {
    if (route.params.quoteId) {
      drId.value = route.params.quoteId
      LocalStorage.setItem('docReq', route.params.quoteId);
    }
  })

  const startOver = () => {
    LocalStorage.removeItem('docReq');
    router.go(0)
  }


</script>

<style lang="scss" scoped>
  .__bg {
    //background: linear-gradient(180deg, white, var(--q-p0), white);
    padding-top: 80px;
    transform: translate(0, -80px);
    color: white;
    background: linear-gradient(0deg, var(--q-a12), black);
  }

  .__pd {
    padding: 10vh max(2vw, 10px);
    min-height: 90vh;
  }

  .__col {
    padding: 20px;
  }

  @media screen and (max-width: 1000px) {
    .__pd {
      padding: 10vw 0;
    }
    .__col {
      padding: 20px 10px;
    }
  }

  .__census {
    width: 100%;
    padding: 30px min(25px, 2vw);
    border-radius: 12px;
    box-shadow: 2px 2px 8px #dedede;
    background: white;
  }

</style>
