import {AnyObj} from 'src/utils/types';
import {watch, ref, computed, Ref, ComputedRef} from 'vue';
import {useRoute} from 'vue-router';
import {_get} from 'symbol-syntax-utils';

export type GetOptions = {
    name?: string,
    store?: any|ComputedRef<any>,
    idPath?: string | string[],
    value?: Ref<AnyObj> | ComputedRef<AnyObj | null>,
    routeParamsPath?: string,
    routeQueryPath?: string,
    refresh?: boolean,
    refreshWhen?: ComputedRef<boolean>|Ref<boolean>,
    refreshOn?:(val?:any) => boolean,
    alwaysLoad?: boolean,
    params?: Ref<AnyObj> | ComputedRef<AnyObj>,
    loadOnlyIf?: boolean | (() => boolean),
    skipInit?: boolean,
    log?: boolean,
    onWatch?: (item: AnyObj, ov?:any) => void,
    deepWatch?: boolean,
    def?:any,
    onLoad?: (item:any) => void,
    useAtcStore?: () => any  // Add this as an optional parameter
};

export const idGet = (
    options: GetOptions
) => {
    const {
        name,
        log,
        store,
        idPath = '_id',
        value = ref(null),
        routeParamsPath,
        routeQueryPath,
        refresh,
        refreshWhen,
        refreshOn,
        alwaysLoad,
        params,
        skipInit,
        onWatch,
        deepWatch,
        onLoad,
        def = {},
        useAtcStore
    } = options;

    const route = useRoute();
    const atcStore = useAtcStore ? useAtcStore() : null;
    const defFn = () => false;
    const loadedOnce = ref(false);
    const refreshed = ref(false);
    const refreshFn = refreshOn || defFn
    const refreshWatch = computed(() => refresh || refreshWhen?.value)

    const id = computed(() => {
        let v;
        const idp = idPath ? Array.isArray(idPath) ? idPath : [idPath] : []
        if (routeParamsPath) v = route.params[routeParamsPath];
        else if (routeQueryPath) v = route.query[routeQueryPath];
        const val = value?.value || value;
        if (!v) return _get(val, idp, val && typeof val !== 'object' ? val : undefined);
        else return v
    })


    const loadCheck = computed(() => {
        if (Object.keys(options).includes('loadOnlyIf')) {
            if (typeof options.loadOnlyIf === 'function') return options.loadOnlyIf();
            else return options.loadOnlyIf;
        } else return true;
    })

    const pending = ref(false);
    const STORE = computed(() => store?.value ? store.value : store);
    const tryAgain = ref();
    const loadOnce = async (tries = 0) => {
        if (STORE.value && loadCheck.value) {
            let pdg = atcStore.get_state(STORE.value.servicePath, id.value)
            // console.log('pdg', pdg, STORE.value.servicePath, id.value)
            if(!pdg) pdg = (STORE.value.patchPendingById || {})[id.value as any]
            if(pdg){
                // console.log('get pending');
                if(tries < 5) {
                    if(tryAgain.value) clearTimeout(tryAgain.value);
                    tryAgain.value = setTimeout(async () => {
                        return await loadOnce(tries + 1)
                    }, 500);
                }
            } else {
                pending.value = true;
                let inStore;
                if (id.value && typeof id.value === 'string') {
                    inStore = STORE.value.getFromStore(id.value);
                }
                if (!inStore?.value?._id || refreshWatch.value || refreshFn(inStore?.value)) {
                    if (log) console.log('loading once');
                    setItem.value = await STORE.value.get(id.value, params?.value)
                        .catch((err: any) => {
                            pending.value = false;
                            console.error(`Error Loading in ${name}: ${err.message}`)
                            return {}
                        })
                } else setItem.value = inStore.value;
                pending.value = false;
                return
            }
            if(onLoad) onLoad(setItem.value)
        } else return

    };

    const refreshItem = async () => {
        await loadOnce();
        refreshed.value = true
    };

    const setItem: Ref<any> = ref(undefined);

    const item = computed(() => {
        const val = value?.value;
        if(typeof val !== 'object') return setItem.value || def;
        const idp = idPath ? Array.isArray(idPath) ? idPath : [idPath] : []

        if (_get(val, idp) && (!alwaysLoad && !refreshed.value)) return val;
        else return setItem.value || value.value || def;
    })


    watch(id, async (nv, ov) => {
        if (store && nv && (!ov || JSON.stringify(nv) !== JSON.stringify(ov))) {
            if(log) console.log('saw id change', nv);
            if (refreshWatch.value || refreshFn(item.value) || (!_get(item.value, '_id') && (!skipInit || loadedOnce.value))) {
                setTimeout(async () => {
                    if(log) console.log('trigger load once');
                    await loadOnce();
                    loadedOnce.value = true;
                }, Math.random() * 10)
            }
        } else if(!nv) setItem.value = undefined
    }, {immediate: true})

    const maybeWatchItem = () => {
        watch(item, (nv, ov) => {
            if(nv && nv._id !== ov?._id){
                (onWatch as any)(nv, ov)
            }
        }, { immediate: true, deep:deepWatch })
    }

    if(onWatch) maybeWatchItem()

    return {
        maybeWatchItem,
        item,
        refreshItem,
        loadOnce,
        loadedOnce,
        id,
        pending
    }
};
