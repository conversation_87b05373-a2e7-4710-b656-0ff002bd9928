<template>
  <q-layout view="hHh Lpr lff" class="bg-transparent __acctLayout">
    <q-header class="__mh" bordered>
      <q-toolbar
          :style="{ backgroundColor: 'var(--q-ir-grey-1)', height: scrolled ? '30px' : '80px', transition: 'all .3s ease-out' }">

        <div class="row items-center no-wrap relative-position _fw _fh">

          <q-btn dense class="__menu_btn" text-color="black" icon="mdi-menu" flat
                 @click="drawer = !drawer"></q-btn>

          <div class="w300 mw60 text-black q-pa-sm">
            <org-context-item></org-context-item>
          </div>

          <q-space></q-space>
          <div class="q-px-md">
            <profile-button
            ></profile-button>
          </div>

        </div>


      </q-toolbar>
    </q-header>

    <q-drawer
        v-model="drawer"
        bordered
        :width="drawerWidth($q.screen)"
        show-if-above
    >
      <div class="__drawer alt-font q-pb-xl">
        <div class="__top">

          <q-list>
            <q-item :inset-level=".3" v-if="$q.screen.lt.md">
              <q-item-section avatar>
                <q-img class="h30 w30" fit="contain" :src="logo"></q-img>
              </q-item-section>
            </q-item>
            <q-item :inset-level=".3" clickable @click="router.push('/')">
              <q-item-section avatar>
                <div class="h25 w25 q-pb-xs q-px-xs">
                  <q-img style="filter: brightness(0)" class="_fa" fit="contain" :src="logo"></q-img>
                </div>
              </q-item-section>
              <q-item-section>
                <q-item-label :class="`tw-six ${$route.name === 'org-dash' ? 'text-p6' : ''}`">Home</q-item-label>
              </q-item-section>
            </q-item>

            <q-expansion-item
                :default-opened="link.on"
                :model-value="link.on"
                v-for="(link, i) in links"
                :key="`link-${i}`"
                hide-expand-icon
                dense
                style="padding: 0"
                :class="`bg-${link.on ? 'white' : 'transparent'}`"
                @update:model-value="openExp($event, link)">
              <template v-slot:header>
                <q-item class="_fw">
                  <q-item-section avatar>
                    <q-icon size="24px" :name="link.icon"></q-icon>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label :class="`tw-six ${link.on ? ' text-p6' : ''}`">{{ link.label }}
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <div :class="`q-pb-md _fw bg-${link.on ? 'white' : 'transparent'}`">
                <q-list separator v-for="(item, idx) in Object.keys(link.items)" :key="`item-${i}-${idx}`">
                  <!--                <div class="q-pl-lg q-py-sm text-p7 font-3-4r tw-six" header v-if="item?.length">{{ item }}</div>-->
                  <template
                      v-for="(sub, index) in Object.keys(link.items[item])"
                      :key="`sub-${i}-${idx}-${index}`">
                    <q-item
                        :inset-level=".5"
                        clickable
                        @click="$router.push(link.items[item][sub].link?.route)">
                      <q-item-section>
                        <q-item-label :class="`text-${link.items[item][sub].on ? 'p6 tw-six' : ''}`">
                          {{ link.items[item][sub].label || sub }}
                        </q-item-label>
                      </q-item-section>

                    </q-item>
                    <q-list class="bg-p0" v-if="link.items[item][sub].on">
                      <q-item
                          :inset-level=".65" v-for="(subub, ii) in link.items[item][sub].subs || []"
                          :key="`subub-${i}${idx}${index}${ii}`"
                          clickable
                          @click="$router.push(subub.link.route)"
                      >

                        <q-item-section>
                          <q-item-label>
                            <span :class="`alt-font text-p6 tw-${subub.on ? 'six' : 'five'}`">{{subub.label}}</span>
                          </q-item-label>
                        </q-item-section>
                        <q-item-section side>
                          <q-avatar v-if="subub.on" size="15px" color="p2"></q-avatar>
                        </q-item-section>
                      </q-item>
                    </q-list>
                  </template>
                </q-list>
              </div>
            </q-expansion-item>
          </q-list>

        </div>

      </div>
    </q-drawer>

    <q-page-container>
      <router-view/>
    </q-page-container>

    <q-footer>
      <account-footer @link="goLink"></account-footer>
    </q-footer>

    <ims-popup :open="chatWindow" @update:open="envStore.setChatWindow"></ims-popup>
  </q-layout>
</template>

<script setup>
  import logo from 'src/assets/commoncare_icon.svg'
  import ProfileButton from 'src/layouts/utils/ProfileButton.vue';
  import OrgContextItem from 'components/orgs/utils/OrgContextItem.vue';
  import AccountFooter from 'layouts/AccountFooter.vue';
  import ImsPopup from 'components/ims/in-app/cards/ImsPopup.vue';

  import {computed, onMounted, ref} from 'vue';
  import {accountNav} from './utils/account-nav';
  import {visitorPrints} from './utils/prints';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage, SessionStorage} from 'symbol-auth-client';
  import {useOrgs} from 'stores/orgs';
  import {HFind} from 'src/utils/hFind';
  import {usePlans} from 'stores/plans';
  import {loginPerson} from 'stores/utils/login';
  const { person } = loginPerson()

  const { orgId, envStore } = trackContext();

  const orgStore = useOrgs();
  const planStore = usePlans();

  import {useRouter} from 'vue-router';
  import {checkDeleteBillEraserDB} from 'components/bill-collective/utils/eraser-db';
  import {storeToRefs} from 'pinia';
  import {trackContext} from 'layouts/utils/track-context';

  const { chatWindow } = storeToRefs(envStore)

  const router = useRouter();

  const scrolled = ref(false);

  const { item: org } = idGet({
    store: orgStore,
    value: orgId
  })

  const { h$: p$ } = HFind({
    store: planStore,
    limit: ref(5),
    params: computed(() => {
      return {
        query: {
          org: orgId.value,
          groups: { $in: person.value?.inGroups || [] }
        }
      }
    })
  })

  const { links } = accountNav(org.value, { plans: computed(() => p$.data) })

  const drawer = ref(false);

  const afterFn = () => {
    const fp = SessionStorage.getItem('sessionPrint')
    const { city, country, region, lngLat } = fp?.ipInfo || {}
    envStore.setAddress({ city, country, region, latitude: lngLat[1], longitude: lngLat[0] })
  }
  const { route } = visitorPrints({afterFn})

  const drawerWidth = (screen) => {
    if (screen.lt.md) return Math.min(screen.width * .8, 250)
    else return 230;
  }

  const goLink = (go) => {
    if (go.route) router.push(go.route)
  }
  const openExp = (val, link) => {
    if(val) router.push(link.link.route)
    else {
      setTimeout(() => {
        if (link.on) router.push(link.link.route)
      }, 50)
    }
  }
  onMounted(() => {
    checkDeleteBillEraserDB()
    if(route.query.refId) LocalStorage.setItem('ref_id', route.query.refId)
    if(route.query.planId) envStore.setPlanId(route.query.planId)
    document.addEventListener('scroll', () => {
      if (window.scrollY > 80) {
        scrolled.value = true;
      } else {
        scrolled.value = false;
      }
    });
  })

</script>

<style scoped lang="scss">
  .__acctLayout {
    width: 100vw;
    overflow: hidden;
  }

  .__menu {
    .__c {
      border-radius: 12px !important;
      border: solid 2px var(--q-primary);
      width: 100%;
      background: white !important;
      transition: all .5s;
      min-width: 200px;
    }

    .__column {
      width: 200px;
    }
  }

  .__lg {
    padding: 2px 2vw;
  }

  .__drawer {
    background: var(--q-ir-grey-1);
    width: 100%;
    height: 100vh;
    overflow-y: scroll;
    color: #101010;
    display: grid;
    grid-template-columns: 100%;
    grid-template-rows: 1fr auto;

    .__top {
      overflow-y:scroll;
      height: 100%;
      width: 100%;
    }

    .__link {
      font-size: .6rem;
    }

    .__sub {
      padding: 5px 10px;
      background: #eee;
    }

  }

  .__mh {
    background-color: white;
    transition: all .5s ease;
  }

  .__main_head {
    background-color: transparent !important;
  }

  .__l {
    width: 150px;
    max-width: 50vw;
    max-height: 80%;
    cursor: pointer;
    transition: all .2s;
  }

  .__s {
    width: 150px;
  }

  .__i {
    width: 50px;
    max-height: 90%;
    cursor: pointer;
  }
</style>
