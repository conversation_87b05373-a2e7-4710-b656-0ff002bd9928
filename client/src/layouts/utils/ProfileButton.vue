<template>
  <div class="cursor-pointer">
    <default-avatar
        v-if="!!isAuth"
        v-bind="{
    modelValue: person,
    defaultAvatar: mystery,
    dark: false,
    useAtcStore: useAtcStore,
    ...$attrs
      }">
      <template v-slot:menu>
        <q-menu>
          <div class="__c">
            <div class="_fw q-pa-sm">
              <org-context-item v-if="orgContext"></org-context-item>
            </div>
            <q-item
                clickable
                @click="item.click()"
                v-for="(item, i) in items"
                :key="`item-${i}`"
                dense
            >
              <q-item-section avatar>
                <q-icon :name="item.icon"></q-icon>
              </q-item-section>
              <q-item-section>
                <q-item-label class="text-weight-bold alt-font">{{ item.label }}</q-item-label>
              </q-item-section>
            </q-item>
          </div>
        </q-menu>
      </template>
    </default-avatar>
    <q-btn
        v-else
        rounded
        flat
        class="bg-white"
        color="white"
        glossy
        label="Login"
        text-color="black"
        no-caps
        @click="loginDialog = true"
    ></q-btn>

    <common-dialog v-model="loginDialog"  size="smmd">
      <div class="q-pa-md _fw">
         <auth-card @authed="loginDialog = false"></auth-card>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import mystery from 'src/assets/icons/mystery.svg'
  import DefaultAvatar from 'src/components/common/avatars/DefaultAvatar.vue';
  import OrgContextItem from 'components/orgs/utils/OrgContextItem.vue';
  import AuthCard from 'components/auth/AuthCard.vue';

  import {loginPerson} from 'src/stores/utils/login';
  import {useAtcStore} from 'stores/atc-store';
  const { person } = loginPerson({ watch: true })

  import {computed, ref} from 'vue';
  import {useRouter} from 'vue-router';
  import {SessionStorage} from 'symbol-auth-client';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  const props = defineProps({
    orgContext: Boolean
  })

  const loginDialog = ref(false)

  const isAuth = computed(() => !!person.value?._id);

  const router = useRouter();

  const items = computed(() => {
    return [
      // {
      //   label: 'Account',
      //   icon: 'mdi-home-circle',
      //   click: () => {
      //     SessionStorage.setItem('_r_mode', 'account');
      //     router.push('/');
      //   }
      // },
      {
        label: 'Profile',
        icon: 'mdi-account-box',
        click: () => {
          SessionStorage.setItem('_r_mode', 'account');
          router.push('/profile');
        }
      },
      {
        icon: 'mdi-logout',
        label: 'Logout',
        click: () => router.push('/logout')
      }
    ]
  })
</script>

<style lang="scss" scoped>
  .__c {
    width: 250px;
    padding: 20px 10px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, .4);
  }
</style>
