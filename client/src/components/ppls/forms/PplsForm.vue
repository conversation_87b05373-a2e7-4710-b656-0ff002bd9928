<template>
  <div class="_fw">
    <!--    NAME-->
    <editable-item
        :editing="isEdit('name') || !form.name"
        label="Name"
    >
      <template v-slot:editing>
        <q-input :model-value="form.name" filled dense @update:model-value="updateForm($event, 'name'), edit = 'name'"></q-input>
      </template>
      <template v-slot:default>
        <default-chip :chip-attrs="{ clickable: true, color: 'white' }" @click="edit = 'name'" :model-value="form" :use-atc-store="useAtcStore"></default-chip>
      </template>
    </editable-item>
    <!--    END NAME-->

    <!--    PHONE-->
    <editable-item
        label="Phones"
        tooltip="Phones"
    >
      <template v-slot:right>
        <primary-list-input
            type="phone"
            :plural="form.phones"
            :single="form.phone"
            :field-attrs="{ filled: true }"
            placeholder=""
            :show="isEdit('phone')"
            @edit="edit = 'phone'"
            :chip="{ color: 'white' }"
            @update:plural="updateForm($event, 'phones')"
            @update:single="updateForm($event, 'phone')"
        ></primary-list-input>
      </template>
    </editable-item>
    <!--    END PHONE-->

    <!--    EMAIL-->
    <editable-item
        label="Emails"
        tooltip="Emails"
    >
      <template v-slot:right>
        <primary-list-input
            @edit="edit = 'email'"
            type="email"
            :show="isEdit('email')"
            :plural="form.emails"
            :single="form.email"
            :field-attrs="{ filled: true }"
            :chip="{ color: 'white' }"
            @update:plural="updateForm($event, 'emails')"
            @update:single="updateForm($event, 'email')"
        ></primary-list-input>
      </template>
    </editable-item>
    <!--    END EMAIL-->

    <!--    ORGS-->
    <editable-item
        v-if="!omitFields?.includes('inOrgs')"
        label="In Groups"
    >
      <template v-slot:right>
        <org-select
            multiple
            v-model="form.inOrgs"
            dense
            filled
            :query="orgQuery"
        ></org-select>
      </template>
    </editable-item>
    <!--    END ORGS-->

    <q-slide-transition>
      <div v-if="isVDirty" class="row justify-end q-py-sm">
        <q-btn icon-right="mdi-content-save" outline color="primary" label="Save" @click="save"></q-btn>
      </div>
    </q-slide-transition>


  </div>
</template>

<script setup>

  import EditableItem from 'src/components/common/forms/EditableItem.vue';
  import {HForm} from 'src/utils/hForm';
  import {usePpls} from 'src/stores/ppls';

  const store = usePpls();
  import OrgSelect from 'src/components/orgs/lists/OrgSelect.vue';
  import PrimaryListInput from 'src/components/common/input/PrimaryListInput.vue';
  import {computed, ref} from 'vue';
  import DefaultChip from 'src/components/common/avatars/DefaultChip.vue';
  import {useAtcStore} from 'stores/atc-store';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    editing: Boolean,
    modelValue: { required: false },
    org: { required: false },
    orgQuery: Object,
    omitFields: {
      type: Array,
      default: () => ['inOrgs']
    },
    formDefaults: Object
  });

  const edit = ref('');
  const isEdit = (val) => {
    return props.editing || edit.value === val;
  };

  const mv = computed(() => {
    return props.modelValue;
    // const list = props.org ? [props.org._id || props.org] : [];
    // return {
    //   inOrgs: list,
    //   ...props.modelValue
    // };
  });


  const { form, save, isVDirty, updateForm } = HForm({
    store,
    log: false,
    reload: true,
    afterFn: (val) => emit('update:model-value', val),
    value: mv,
    formDefaults: props.formDefaults,
    vOpts: ref({
      name: { name: 'Name', v: ['notEmpty'] }
    })
  });
</script>

<style scoped>

</style>
