<template>
  <div class="_fw">
    <div class="_f_l _s_chip">{{ form._id ? 'Edit' : 'Add' }} Bundle</div>
    <div class="_form_grid _f_g_r q-py-sm">
      <div class="_form_label">Name</div>
      <div class="q-pa-sm">
        <q-input @update:model-value="autoSave('name')" v-model="form.name" filled dense></q-input>
      </div>
      <div class="_form_label">Description</div>
      <div class="q-pa-sm">
        <q-input @update:model-value="autoSave('description')" autogrow v-model="form.description" filled
                 dense></q-input>
      </div>
      <div class="_form_label">Public</div>
      <div class="q-pa-sm">
        <q-checkbox :model-value="!!form.public" @update:model-value="setForm('public', $event)"
                    label="Visible/Available to the public"></q-checkbox>
      </div>
      <template v-if="!form._id">
        <div class="_form_label"></div>
        <div class="q-pa-sm">
          <div class="row justify-end">
            <q-btn class="_s_btn" no-caps push label="Create Bundle" @click="save"></q-btn>
          </div>
        </div>
      </template>
    </div>
    <template v-if="form._id">

      <div class="_f_l _s_chip">Prices</div>
      <div class="q-pa-sm">
        <div class="font-3-4r">If you want to rename procedures (vs what CPT codes have listed), add descriptions,
          or show line-item pricing - click on a procedure to edit details. Details are not necessary as bundles are
          bundled pricing.
        </div>
        <book-prices v-if="form._id" can-edit :bundle="form"></book-prices>
      </div>

      <div class="_f_l _s_chip">Delete Bundle</div>
      <div class="q-pa-sm">
        <remove-button label="Click To Remove Bundle" name="Bundle" @remove="remove"></remove-button>
      </div>
    </template>


  </div>
</template>

<script setup>
  import BookPrices from 'components/providers/bundles/forms/BookPrices.vue';
  import RemoveButton from 'components/common/buttons/RemoveButton.vue';

  import {HForm, HSave} from 'src/utils/hForm';
  import {useBundles} from 'stores/bundles';
  import {computed} from 'vue';
  import {$errNotify, $successNotify} from 'src/utils/global-methods';
  import {idGet} from 'src/utils/id-get';

  const store = useBundles();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    provider: { required: false },
    modelValue: { required: false }
  })

  const { item: pb } = idGet({
    store,
    value: computed(() => props.modelValue)
  })

  const { form, save } = HForm({
    store,
    value: pb,
    beforeFn: (item) => {
      if (!item.provider) item.provider = props.provider._id || props.provider;
      return item
    },
    afterFn: (val) => {
      emit('update:model-value', val);
    }
  })

  const { autoSave, setForm } = HSave({ form, store, pause: computed(() => !form.value._id) })

  const setMeta = async (procedure, meta) => {
    form.value.meta = { ...form.value.meta, [procedure._id]: meta }
    await store.patch(form.value._id, { $set: { [`meta.${procedure._id}`]: meta } })
        .catch(err => $errNotify(`Error adding procedure details: ${err.message}`))
  }

  const remove = () => {
    store.remove(form.value._id)
        .catch(err => $errNotify(`Error removing: ${err.message}`));
  }

  const add = async (val) => {
    const list = [...form.value.procedures || []];
    list.push(val);
    form.value.procedures = list
    if (form.value._id) {
      await store.patch(form.value._id, { $addToSet: { procedures: val._id } })
      $successNotify('Added Procedure')
    }
  }


</script>

<style lang="scss" scoped>

</style>
