<template>
  <div class="_fw">
    <div class="row">
      <div :class="`col-12 col-md-${stack ? '12' : '6'} __pd`">
        <div class="__top">
          <div class="flex items-center q-pt-md">
            <div class="font-7-8r text-grey-9">In or near:</div>
            <fips-picker
                @update:state="setState"
                @update:city="setCity"
                @update:zip="setZip"
            ></fips-picker>
          </div>
          <div class="row items-center">
            <div class="q-px-sm q-py-md _fw">
              <q-input dense placeholder="Practitioner or business name..." v-model="search.text"
                       @update:model-value="runSearch"></q-input>
            </div>
            <!--            <div class="col-6 q-px-xs">-->
            <!--              <q-input dense label="First Name" v-model="query.firstName" @update:model-value="runSearch()"-->
            <!--                       class="q-my-md"></q-input>-->

            <!--            </div>-->
            <!--            <div class="col-6 q-px-xs">-->
            <!--              <q-input dense label="Last Name" v-model="query.lastName" @update:model-value="runSearch()"-->
            <!--                       class="q-my-md"></q-input>-->
            <!--            </div>-->
          </div>

          <div class="font-7-8r text-grey-9">Type of practitioner:</div>
          <div class="flex items-center">
            <q-icon v-if="!pTypes[query.primaryType]" color="primary" name="mdi-stethoscope"></q-icon>
            <span class="font-1r" v-else>{{ pTypes[query.primaryType].icon }}</span>
            <q-chip color="white" clickable @click="searchProvider = !searchProvider" :removable="!!query.primaryType"
                    icon-remove="mdi-close" @remove="removeType">
              <span>{{ providerLabel }}</span>
              <q-icon size="20px" class="q-ml-sm" :name="`mdi-menu-${searchProvider ? 'up' : 'down'}`"
                      color="p7"></q-icon>
            </q-chip>
          </div>

          <q-slide-transition>
            <div class="_fw" v-if="searchProvider">
              <provider-type-picker
                  v-model="query.primaryType"
                  :specialty="taxonomy3"
                  @update:specialty="setSpecialty"
                  @update:model-value="runSearch"
              ></provider-type-picker>
            </div>
          </q-slide-transition>

        </div>
        <q-slide-transition>
          <div class="_fw" v-if="display">
            <div class="row justify-end">
              <q-btn dense flat size="sm" color="red" icon="mdi-close" @click="closeDisplay"></q-btn>
            </div>
            <provider-item :model-value="display"></provider-item>
            <div class="row justify-end items-center q-py-sm">
              <q-btn push no-caps class="tw-six _p_btn" label="Choose Provider"
                     @click="$emit('update:model-value', display)"></q-btn>
            </div>
          </div>
        </q-slide-transition>
        <template v-if="!display">

          <div class="__list">
            <q-spinner color="primary" size="30px" v-if="loading"></q-spinner>
            <div class="q-py-sm row items-center" v-if="s$.total">
              <div>1 - {{ Math.min(limit, s$.total) }} of top {{ s$.total }}</div>
              <q-btn dense flat size="sm" icon="mdi-refresh" color="primary" @click="runSearch">
                <q-tooltip>Load More</q-tooltip>
              </q-btn>
            </div>
            <q-list class="bg-transparent" v-if="!hasReq">
              <q-item-label v-if="!familyProviders.total" header>Enter search criteria to see results</q-item-label>
              <template v-else>
                <div class="text-grey-6 q-pa-sm alt-font">Your Providers</div>
                <q-separator></q-separator>
              </template>
              <provider-item
                  simple
                  clickable
                  @click="selectItem(p)"
                  v-for="(p, i) in familyProviders.data"
                  :key="`fp-${i}`"
                  :model-value="p"
              ></provider-item>
            </q-list>
            <q-list separator v-else>

              <provider-item
                  :review="review"
                  v-for="(opt, i) in pList"
                  :key="`doc-${i}`"
                  :model-value="opt"
                  clickable
                  @click="selectedItem = opt"
              >
                <template v-slot:side>
                  <q-item-section side @click="emit('update:model-value', opt)">
                    <q-avatar size="20px" color="primary" v-if="activeItem === i"></q-avatar>
                  </q-item-section>
                </template>
              </provider-item>

              <q-item dense v-if="limit < s$.total" clickable @click="limit = Math.min(limit + 10, s$.total)">
                <q-item-section>
                  <q-item-label class="text-grey-9">+ Show More...</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </template>

      </div>
      <div :class="`col-12 col-md-${stack ? '12' : '6' } __pd relative-position`">
        <div class="__map">
          <mapbox
              v-model:selected="selectedIndex"
              v-model:active-item="activeItem"
              :center="_location.lngLat"
              :markers-in="points"
              :zoom="mapZoom"
          ></mapbox>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup>
  import FipsPicker from 'components/coverages/marketplace/forms/FipsPicker.vue';
  import ProviderTypePicker from 'components/providers/forms/ProviderTypePicker.vue';
  import ProviderItem from 'components/providers/cards/ProviderItem.vue';
  import Mapbox from 'components/common/mapbox/map/MapBox.vue';

  import {computed, onMounted, ref, watch} from 'vue';
  import {useProviders} from 'stores/providers';
  import {getStateLngLat, getStateCode} from 'components/common/geo/data/states';
  import {providerTypes} from 'components/providers/utils/types';
  import {createGeoJSONCircle} from 'src/utils/geo-utils';
  import {HQuery} from 'src/utils/hQuery';
  import {_flatten} from 'symbol-syntax-utils';
  import {loginPerson} from 'stores/utils/login';
  const { person } = loginPerson()

  import {storeToRefs} from 'pinia';
  import {useEnvStore} from 'stores/env';
  import {idGet} from 'src/utils/id-get';
  import {useHouseholds} from 'stores/households';
  import {HFind} from 'src/utils/hFind';

  const envStore = useEnvStore()
  const { lngLat, region, city, address } = storeToRefs(envStore);

  const store = useProviders();
  const hhStore = useHouseholds();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: false },
    searchDefault: { required: false },
    multiple: Boolean,
    stack: Boolean,
    query: Object,
    review: Boolean
  })

  const { item: hh } = idGet({
    store: hhStore,
    value: computed(() => person.value?.household)
  })

  const selected = ref(undefined);
  const loading = ref(false);
  const error = ref(undefined);
  const activeItem = ref(-1);
  const selectedItem = ref(undefined);
  const mapZoom = ref(8);
  const limit = ref(25);
  const searchProvider = ref(false);
  const pTypes = ref(providerTypes);
  const sState = ref('');
  const sCity = ref('');
  const zip = ref('');
  const _location = ref({
    lngLat: undefined,
    km: 30
  })

  const selectItem = (val) => {
    selectedItem.value = val;
    emit('update:model-value', val);
  }

  const taxonomy3 = ref('');

  const s$ = ref({ data: [], total: 0 })

  const pList = computed(() => {
    return s$.value.data || [];
  })

  const selectedIndex = computed(() => {
    if (!selectedItem.value?._id) return -1;
    return (pList.value || []).map(a => a._id).indexOf(selectedItem.value._id)
  })

  const display = computed(() => {
    if (selectedItem.value) return selectedItem.value
    if (activeItem.value > -1) return (s$.value.data || [])[activeItem.value];
    else return undefined
  })

  const closeDisplay = () => {
    activeItem.value = -1;
    selectedItem.value = undefined;
  }

  const query = ref({ ...props.query });
  const { search, searchQ } = HQuery({});
  const removeType = () => {
    delete query.value.primaryType;
    taxonomy3.value = '';
  }
  const providerLabel = computed(() => {
    return !taxonomy3.value ? query.value?.primaryType ? pTypes.value[query.value.primaryType]?.label || 'Select Type' : 'Select Type' : taxonomy3.value === 'No Specialization' ? pTypes.value[query.value.primaryType]?.label || 'Select Type' : taxonomy3.value;
  })

  const td = ref(undefined);
  // const s$ = ref({ total: 0, data: [] });
  const hasReq = ref(false);


  const locPending = ref(false);
  const setState = (val) => {
    if (val && !locPending.value) {
      _location.value = { lngLat: getStateLngLat(val) };
      sState.value = getStateCode(val);
    }
  }
  const setCity = (val) => {
    locPending.value = true;
    if (val && city.value !== val) {
      mapZoom.value = 10;
      sCity.value = val;
    }
    setTimeout(() => {
      locPending.value = false;
    }, 2000);
  }

  const setZip = (val) => {
    setTimeout(() => {
      if (val && address.value?.postal !== val) {
        mapZoom.value = 10;
        zip.value = val;
      }
    }, 100)

  }

  const points = computed(() => {
    return _flatten(s$.value?.data.map(d => (d.locations || []).map(a => [a.longitude, a.latitude]).filter(a => !!a[0] && !!a[1])));
  })

  const { h$: familyProviders } = HFind({
    store,
    limit: computed(() => (hh.value?.providers || []).length),
    pause: computed(() => !hh.value?.providers),
    params: computed(() => {
      return {
        query: {
          _id: { $in: hh.value?.providers || [] }
        }
      }
    })
  })

  const runSearch = async (rerun) => {
    hasReq.value = true;
    if (!loading.value) {
      loading.value = true;
      const { primaryType } = query.value;
      let text = '';
      if (search.value.text) text += search.value.text;
      if (taxonomy3.value) text += ` ${taxonomy3.value}`
      else if (primaryType) text += ` ${primaryType}`;
      const sity = sCity.value;
      const st = sState.value || getStateLngLat(region.value);
      if(!st) return;
      if (st || sity) {
        text += ` near ${sity ? `${sity}, ` : ''}${st}`
      }
      const geo = createGeoJSONCircle(_location.value.lngLat, _location.value.km || 30);
      console.log('geo', geo);
      const opts = await store.find({
        query: {
          ...query.value,
          ...searchQ.value,
          $limit: 50
        },
        _search: {
          location: _location.value,
          text
        },
        _geo: {
          path: 'geo',
          geo
        }
      })
          .catch(err => {
            console.error(err);
            error.value = err
          });
      if (opts) s$.value = opts
      loading.value = false;

    } else if (!rerun) {
      if (td.value) clearTimeout(td.value);
      td.value = setTimeout(() => {
        runSearch(true);
      }, 1000)

    } else loading.value = false;
  }

  const setSpecialty = (val) => {
    taxonomy3.value = val;
    searchProvider.value = false;
    runSearch()
  }

  watch(() => props.modelValue, (nv) => {
    if (nv) selected.value = nv;
    else if (props.multiple) selected.value = [];
    else selected.value = '';
  }, { immediate: true })

  watch(() => props.searchDefault, (nv) => {
    if (nv && !search.value.text) {
      search.value.text = nv;
      runSearch(nv);
    }
  }, { immediate: true })


  onMounted(() => {
    setTimeout(() => {
      if (lngLat.value) {
        _location.value = { lngLat: lngLat.value };
      } else if (region.value) {
        setState(region.value)
      }
      if (!hasReq.value) {
        // runSearch(false, true)
      }

    }, 500)
  })

</script>

<style lang="scss" scoped>
  .__pd {
    padding: min(2vh, 1vw);
  }

  .__top {
    min-height: 225px;
    width: 100%;
    border-radius: 7px;
    //border: solid 1px #999;
    box-shadow: 0 4px 8px -4px #999;
    padding: 10px 20px;
    margin-bottom: 15px;
    background: white;
  }

  .__map {
    margin-bottom: 20px;
    box-shadow: 0 4px 8px -4px #999;
    width: 100%;
    position: relative;
    height: 500px;
    border-radius: 7px;
    overflow: hidden;
    background: linear-gradient(12deg, #eee, #efefef, #eee);
  }
  @media screen and (max-width: 1023px) {
    .__map {
      height: 250px;
    }
  }

  .__list {
    border-radius: 7px;
    width: 100%;
    padding: 10px;
    box-shadow: 0 4px 8px -4px #999;
    max-height: min(400px, 90vh);
    overflow-y: scroll;
    background: white;
  }




</style>
