<template>
  <div class="row items-center">
    <div class="col-12">
      <div class="row items-center">
        <div class="col-6 q-px-sm">
          <q-input :disable="!isAllowedToEdit" v-model="form.firstName" filled label="First Name" autogrow></q-input>

        </div>
        <div class="col-6 q-px-sm">
          <q-input :disable="!isAllowedToEdit" v-model="form.lastName" filled label="Last Name" autogrow></q-input>

        </div>

      </div>
      <div class="q-pa-sm" v-if="!isAllowedToEdit">You cannot edit this person because they are a member of other organizations you do not have access to.</div>
      <div class="font-3-4r q-py-sm">Enter at least one contact method</div>
      <email-field :disable="!isAllowedToEdit" class="q-mb-sm" hide-bottom-space filled v-model="form.email"></email-field>
      <phone-input :disable="!isAllowedToEdit" :input-attrs="{ filled: true, hideBottomSpace: true }" v-model="form.phone"></phone-input>

      <template v-if="isAllowedToEdit">
      <div class="font-3-4r q-py-sm flex items-center">
        <div>Enter</div>
        &nbsp; <q-chip size="sm" outline icon="mdi-lock" label="Secure Fields" color="ir-blue-10"></q-chip> &nbsp;
        <div>{{form?._id ? '(optional - can be added later)' : ''}}</div>
      </div>
      <dob-input hide-bottom-space class="q-mb-sm" v-model="form.dob" filled></dob-input>
      <ssn-input hide-bottom-space filled v-model="form.ssn"></ssn-input>
      </template>

      <slot name="bottom"></slot>
    </div>
    <div class="q-py-md row justify-end">
      <q-btn v-if="isAllowedToEdit" push glossy color="primary" icon="mdi-content-save" label="Save" no-caps @click="save"></q-btn>
    </div>
  </div>
</template>

<script setup>
  import EmailField from 'src/components/common/input/EmailField.vue';
  import DobInput from 'src/components/common/input/DobInput.vue';
  import SsnInput from 'src/components/common/input/SsnInput.vue';
  import PhoneInput from 'src/components/common/phone/PhoneInput.vue';

  import {computed, ref, watch} from 'vue';

  import { usePpls } from 'stores/ppls';
  import {idGet} from 'src/utils/id-get';
  import {useGroups} from 'stores/groups';
  import {useGrpMbrs} from 'stores/grp-mbrs';
  import {$errNotify} from 'src/utils/global-methods';
  import {loginPerson} from 'stores/utils/login';
  const pplStore = usePpls();
  const groupStore = useGroups();
  const mbrStore = useGrpMbrs();

  const { person:lPerson } = loginPerson();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: Object,
    groupId: { required: true },
  })

  const active = ref('email');

  const formFn = (val) => {
    return {
      ...val
    }
  }

  const { item:person } = idGet({
    store:pplStore,
    value: computed(() => props.modelValue)
  })

  const { item: group } = idGet({
    store: groupStore,
    value: computed(() => props.groupId)
  })

  const form = ref({})

  const save = async () => {
    let mbr = { person: person.value._id, group: group.value._id, org: group.value.org, name: form.value.name || person.value.name, email: form.value.email || person.value.email };
    if(person.value._id){
      mbr = await mbrStore.create(mbr)
          .catch(err => $errNotify(`Error adding member: ${err.message}`))
    } else {
      const prsn = await pplStore.create(form.value)
          .catch(err => $errNotify(`Error creating person: ${err.message}`))
      mbr.person = prsn._id;
      mbr = await mbrStore.create(mbr)
          .catch(err => $errNotify(`Error adding member: ${err.message}`))
    }
    if(mbr?._id) emit('update:model-value', mbr);
    setTimeout(() => {
      if(!props.modelValue) form.value = formFn()
    }, 500)
  }

  const mbrsLoaded = ref(false);
  const mbrs = ref({ data: [] })

  const isAllowedToEdit = computed(() => {
    if(!person.value._id) return true;
    if(!mbrsLoaded.value) return false;
    if(mbrs.value.data?.length > 1){
      if(mbrs.value.data.map(a => a.org).every(a => lPerson.value?.inOrgs?.includes(a))) return true;
      else return false;
    } else return true;
    if(person.value._id && !person.value.login) return true;
  })

  watch(person, (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      form.value = formFn(nv);
      const mbr = mbrStore.find({ query: { person: nv._id, $limit: 10 }})
          .catch(err => {
            console.error(`Error getting group members: ${err.message}`)
            return { data: [] }
          })
      mbrsLoaded.value = true;
      if(mbr?.total) {
        mbrs.value = mbr;
      }
    }
  }, { immediate: true })

  const mv = computed(() => props.modelValue);
  watch(mv, (nv, ov) => {
    if (nv && (!ov || JSON.stringify(nv) !== JSON.stringify(ov))) {
      form.value = formFn(nv)
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>

</style>
